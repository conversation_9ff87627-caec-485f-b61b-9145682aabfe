// components/course/QuizItem.jsx
"use client";
import React from "react";

export default function QuizItem({ quiz, onEdit, onDelete }) {
  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-1">{quiz.title}</h3>
          <p className="text-sm text-gray-600">{quiz.description}</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={onEdit}
            className="text-blue-600 hover:underline text-sm"
          >
            تعديل
          </button>
          <button
            onClick={onDelete}
            className="text-red-600 hover:underline text-sm"
          >
            حذف
          </button>
        </div>
      </div>
    </div>
  );
}
// components/course/ImageCropperModal.jsx
"use client";
import React, { useState, useRef } from "react";
import ReactCrop from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";

export default function ImageCropperModal({ quizId, imageUrl, onClose, onCrop }) {
  const [localCrop, setLocalCrop] = useState({ unit: "%", width: 50, height: 50, x: 25, y: 25 });
  const [localCompletedCrop, setLocalCompletedCrop] = useState(null);
  const localImgRef = useRef(null);

  const getCroppedImg = (image, crop) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;
    canvas.width = crop.width;
    canvas.height = crop.height;
    ctx.drawImage(
      image,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      crop.width,
      crop.height
    );
    return new Promise((resolve) => {
      canvas.toBlob(resolve, "image/jpeg", 0.8);
    });
  };

  const handleLocalCrop = async () => {
    if (localCompletedCrop && localImgRef.current) {
      const croppedBlob = await getCroppedImg(localImgRef.current, localCompletedCrop);
      if (onCrop) {
        onCrop(quizId, croppedBlob);
      }
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-auto">
        <h3 className="text-lg font-bold mb-4 text-center">تعديل الصورة - اسحب لتحديد المنطقة المطلوبة</h3>
        <div className="mb-4 flex justify-center">
          <ReactCrop
            crop={localCrop}
            onChange={setLocalCrop}
            onComplete={setLocalCompletedCrop}
            aspect={undefined}
            minWidth={50}
            minHeight={50}
            keepSelection={true}
            style={{ maxWidth: "100%", maxHeight: "60vh" }}
          >
            <img
              ref={localImgRef}
              src={imageUrl}
              alt="للتعديل"
              style={{ maxWidth: "100%", maxHeight: "60vh", display: "block" }}
            />
          </ReactCrop>
        </div>
        <div className="flex gap-2 justify-center">
          <button
            onClick={handleLocalCrop}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition-colors"
            disabled={!localCompletedCrop}
          >
            تطبيق القص
          </button>
          <button
            onClick={onClose}
            className="bg-gray-300 px-6 py-2 rounded hover:bg-gray-400 transition-colors"
          >
            إلغاء
          </button>
        </div>
        <div className="mt-3 text-center text-sm text-gray-600">
          💡 نصيحة: اسحب الزوايا لتغيير حجم المنطقة، واسحب المنطقة لتحريكها
        </div>
      </div>
    </div>
  );
}

"use client";
import React from "react";
import { useParams } from "next/navigation";
import useCourseDetails from "@/hooks/useCourseDetails";
import CourseHeader from "@/app/_Components/course/CourseHeader";
import CourseInfoSection from "@/app/_Components/course/CourseInfoSection";
import CourseStatsSection from "@/app/_Components/course/CourseStatsSection";
import CourseMediaSection from "@/app/_Components/course/CourseMediaSection";
import LessonsSection from "@/app/_Components/course/LessonsSection";
import ImageCropperModal from "@/app/_Components/course/ImageCropperModal";

export default function CourseDetails() {
  const params = useParams();
  const courseId = params.id;
  
  const {
    courseData,
    lessons,
    loading,
    error,
    publishing,
    handlers,
    states
  } = useCourseDetails(courseId);



  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  // No course data
  if (!courseData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-600">لم يتم العثور على بيانات الكورس</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-lg p-6">
        {/* Error Display */}
        {states.error && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">{states.error}</p>
            <button
              onClick={() => handlers.clearError && handlers.clearError()}
              className="mt-2 text-red-500 hover:text-red-700 text-sm underline"
            >
              إغلاق
            </button>
          </div>
        )}

        {/* Header */}
        <CourseHeader
          courseId={courseId}
          courseData={courseData}
          publishing={publishing}
          onTogglePublish={handlers.togglePublish}
        />

        {/* Course Information Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Column - Course Info & Stats */}
          <div className="space-y-4">
            <CourseInfoSection courseData={courseData} />
            <CourseStatsSection courseData={courseData} />
          </div>

          {/* Right Column - Media & Pricing */}
          <div className="space-y-4">
            {/* Pricing Section */}
            <div>
              <h2 className="text-xl font-semibold text-gray-700 mb-2">الأسعار</h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-600">
                  <span className="font-medium">السعر:</span> {courseData.price}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">سعر الخصم:</span> {courseData.discount_price}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">العملة:</span> {courseData.currency}
                </p>
              </div>
            </div>
            
            <CourseMediaSection courseData={courseData} />
          </div>
        </div>

        {/* Lessons Section */}
        <LessonsSection 
          courseId={courseId}
          lessons={lessons}
          handlers={handlers}
          states={states}
        />

        {/* Image Cropper Modal */}
        {Object.keys(states.showImageCropper || {}).some(key => states.showImageCropper[key]) && (
          <ImageCropperModal
            quizId={Object.keys(states.showImageCropper).find(key => states.showImageCropper[key])}
            imageUrl={states.imagePreview[Object.keys(states.showImageCropper).find(key => states.showImageCropper[key])]}
            onClose={() => {
              const quizId = Object.keys(states.showImageCropper).find(key => states.showImageCropper[key]);
              handlers.setShowImageCropper(prev => ({ ...prev, [quizId]: false }));
            }}
            onCrop={(quizId, croppedBlob) => {
              handlers.setCroppedImages(prev => ({ ...prev, [quizId]: croppedBlob }));
            }}
          />
        )}
      </div>
    </div>
  );
}

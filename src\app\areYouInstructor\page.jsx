"use client";
import React, { useState } from "react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";

import { useSelector } from "react-redux";
import { selectCurrentToken, selectCurrentUser } from "@/store/authSlice";
import { userDataChange } from "../../services/anyUserDataChange";
import LoadingSpinner from "../_Components/LoadingSpinner/LoadingSpinner";

export default function Signup() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const router = useRouter();
  const token = useSelector(selectCurrentToken);
  const user = useSelector(selectCurrentUser);
  const initialValues = {
    phone_number: "",
    is_instructor: null,
  };

  const validate = (values) => {
    const errors = {};
    const regex = {
      phone: /^01[0-2,5]{1}[0-9]{8}$/,
    };
    if (!values.phone_number) {
      errors.phone_number = "رقم الهاتف مطلوب";
    } else if (!regex.phone.test(values.phone_number)) {
      errors.phone_number = "رقم هاتف مصري غير صحيح";
    }
    if (formik.values.is_instructor === null) {
      setError("يرجى تحديد ما إذا كنت مدربًا أم لا");
      setTimeout(() => {
        setError("");
      }, 5000);
    }
    return errors;
  };

  const onSubmit = async (values) => {
    try {
      setIsLoading(true);
      setError(null);

      const updatedFields = {
        phone_number: values.phone_number,
        is_instructor: values.is_instructor,
      };

      await userDataChange(user.id, token, updatedFields);
      setSuccess(true);
      router.push("/"); // أو أي صفحة مناسبة
    } catch (error) {
      console.error("خطأ أثناء تعديل بيانات المستخدم:", error);
      setError("حدث خطأ أثناء حفظ البيانات.");
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik({
    initialValues,
    validate,
    onSubmit,
  });

  const formFields = [
    {
      id: "phone_number",
      label: "رقم الهاتف",
      type: "tel",
      placeholder: "أدخل رقم الهاتف",
    },
  ];

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-800 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
            تم التسجيل بنجاح! جاري التوجيه إلى صفحة تسجيل الدخول...
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={formik.handleSubmit}>
          <div className="rounded-md shadow-sm space-y-4">
            {formFields.map((field) => (
              <div key={field.id}>
                <label
                  htmlFor={field.id}
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {field.label}
                </label>
                <input
                  id={field.id}
                  required
                  name={field.id}
                  type={field.type}
                  placeholder={field.placeholder}
                  className={`relative block w-full appearance-none rounded-md border px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm ${
                    formik.errors[field.id] && formik.touched[field.id]
                      ? "border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values[field.id]}
                />
                {formik.errors[field.id] && formik.touched[field.id] && (
                  <p className="mt-1 text-sm text-red-600">
                    {formik.errors[field.id]}
                  </p>
                )}
              </div>
            ))}
          </div>

          <div className="flex flex-col gap-2">
            <p className="text-sm text-gray-700 dark:text-gray-200">
              هل أنت مدرب؟ <span className="text-red-500">*</span>
            </p>

            <div className="flex justify-center gap-24 items-center">
              <div className="flex items-center gap-2">
                <input
                  id="is_instructor_yes"
                  name="is_instructor"
                  type="radio"
                  value="true"
                  onChange={() => formik.setFieldValue("is_instructor", true)}
                  checked={formik.values.is_instructor === true}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                />
                <label
                  htmlFor="is_instructor_yes"
                  className="ml-2 text-sm text-gray-900 dark:text-gray-300"
                >
                  نعم
                </label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  id="is_instructor_no"
                  name="is_instructor"
                  type="radio"
                  value="false"
                  onChange={() => formik.setFieldValue("is_instructor", false)}
                  checked={formik.values.is_instructor === false}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                />
                <label
                  htmlFor="is_instructor_no"
                  className="ml-2 text-sm text-gray-900 dark:text-gray-300"
                >
                  لا
                </label>
              </div>
            </div>

            {formik.touched.is_instructor && formik.errors.is_instructor && (
              <p className="text-red-500 text-sm">
                {formik.errors.is_instructor}
              </p>
            )}
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative flex w-full justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {isLoading ? "جاري التسجيل..." : "تسجيل"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

"use client";
import React, { useEffect } from "react";
import { submitWalletInfo } from "../../../services/anyUserDataChange";
import { fetchInstructorProfile } from "../../../services/instructor";
import { isValidPhoneNumber } from "libphonenumber-js";
import Cookies from "js-cookie";
import { selectCurrentUser } from "../../../store/authSlice";
import { useParams } from "next/navigation";
import { useState } from "react";
import { useSelector } from "react-redux";
export default function InstructorPayment() {
  const user = useSelector(selectCurrentUser);
  const { id } = useParams();
  const [form, setForm] = useState({
    payment_method: "",
    wallet_number: "",
  });
  const [walletImmutable, setWalletImmutable] = useState(false);
  const [isInstructor, setIsInstructor] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    if (user && user.id && String(user.id) !== String(id)) {
      router.replace("/Notfound");
      return;
    }
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      const token = Cookies.get("authToken");
      if (!token) {
        setError("يرجى تسجيل الدخول");
        setLoading(false);
        return;
      }
      try {
        const res = await fetchInstructorProfile(id, token);
        let paymentMethod = res.payment_method || "";
        let walletNumber = res.wallet_number || "";
        setForm({
          payment_method: paymentMethod,
          wallet_number: walletNumber,
        });
        setIsInstructor(res.is_instructor || false);
        console.log("Instructor Profile Data payment:", res);
        setWalletImmutable(!!walletNumber);
      } catch (err) {
        setError("حدث خطأ أثناء جلب البيانات");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [id, user]);
  const handleChange = (e) => {
    const { name, value, files } = e.target;
    if (name === "payment_method") {
      setForm({ ...form, payment_method: value, wallet_number: "" });
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول");
      setSaving(false);
      return;
    }
    if (isInstructor && form.payment_method && !form.wallet_number) {
      setError("يرجى إدخال رقم المحفظة");
      setSaving(false);
      return;
    }
    if (isInstructor && form.payment_method && form.wallet_number) {
      if (!isValidPhoneNumber(form.wallet_number, "EG")) {
        setError(
          "رقم المحفظة غير صحيح، يرجى إدخال رقم هاتف مصري صحيح (مثال: 01012345678)"
        );
        setSaving(false);
        return;
      }
    }
    try {
      if (isInstructor && !walletImmutable) {
        await submitWalletInfo(id, token, {
          wallet_number: form.wallet_number,
          payment_method: form.payment_method,
        });
        setWalletImmutable(true);
      }

      setSuccess("تم حفظ التعديلات بنجاح");
      setTimeout(() => {
        router.push(`/instructor/dashboard/`);
      }, 2000);
    } catch (err) {
      setError("حدث خطأ أثناء حفظ البيانات");
    } finally {
      setSaving(false);
    }
  };
  return (
    <div>
      {error && (
        <div className="bg-red-100 text-red-800 p-4 rounded mb-4">
          <strong>خطأ:</strong> {error}
        </div>
      )}
      {isInstructor && (
        <form action="">
          <div className="border-t pt-4 mt-4">
            <h2 className="font-bold mb-4">وسيلة استقبال الدفع</h2>
            {walletImmutable ? (
              <div>
                <div>
                  <label className="block mb-1 font-medium">وسيلة الدفع</label>
                  <input
                    type="text"
                    value={
                      form.payment_method === "vodafone_cash"
                        ? "فودافون كاش"
                        : form.payment_method === "orange_money"
                        ? "أورانج موني"
                        : form.payment_method === "we_cash"
                        ? "وي كاش"
                        : form.payment_method === "etisalat_cash"
                        ? "اتصالات كاش"
                        : ""
                    }
                    className="w-full border rounded px-3 py-2 bg-gray-100"
                    disabled
                  />
                </div>
                <div className="mt-4">
                  <label className="block mb-1 font-medium">رقم المحفظة</label>
                  <input
                    type="text"
                    value={form.wallet_number}
                    className="w-full border rounded px-3 py-2 bg-gray-100"
                    disabled
                  />
                  <p className="text-sm text-gray-500 mt-2">
                    لا يمكن تعديل وسيلة الدفع أو رقم المحفظة بعد حفظها. يرجى
                    التواصل مع الأدمن للتعديل.
                  </p>
                </div>
              </div>
            ) : (
              <div>
                <div className="space-y-4">
                  {[
                    "vodafone_cash",
                    "orange_money",
                    "we_cash",
                    "etisalat_cash",
                  ].map((method) => (
                    <div
                      key={method}
                      className="flex items-center space-x-2 space-x-reverse"
                    >
                      <input
                        type="radio"
                        id={method}
                        name="payment_method"
                        value={method}
                        checked={form.payment_method === method}
                        onChange={handleChange}
                        className="ml-2"
                      />
                      <label htmlFor={method}>
                        {method === "vodafone_cash" && "فودافون كاش"}
                        {method === "orange_money" && "أورانج موني"}
                        {method === "we_cash" && "وي كاش"}
                        {method === "etisalat_cash" && "اتصالات كاش"}
                      </label>
                    </div>
                  ))}
                </div>
                {form.payment_method && (
                  <div className="mt-4">
                    <label className="block mb-1">رقم المحفظة</label>
                    <input
                      type="text"
                      name="wallet_number"
                      value={form.wallet_number}
                      onChange={handleChange}
                      className="w-full border rounded px-3 py-2"
                      placeholder="أدخل رقم هاتف مصري (مثال: 01012345678)"
                    />
                    <p className="text-sm text-gray-500 mt-2">
                      يرجى إدخال رقم المحفظة بعناية (رقم هاتف مصري صحيح) لأنه لا
                      يمكن تعديله بعد الحفظ إلا من خلال الأدمن.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
          <button
            type="submit"
            className="w-full bg-primary text-white py-2 rounded-lg font-bold hover:bg-primary/90 transition"
            disabled={saving}
          >
            {saving ? "جارٍ الحفظ..." : "حفظ التعديلات"}
          </button>
        </form>
      )}
    </div>
  );
}

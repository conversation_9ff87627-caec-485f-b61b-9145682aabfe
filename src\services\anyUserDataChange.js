// services/anyUserDataChange.js
import axios from "axios";
import { API_BASE_URL } from '../config/api';

export async function userDataChange(userId, token, data, isFormData = false) {
    const headers = {
        Authorization: `Bearer ${token}`,
        ...(isFormData ? {} : { "Content-Type": "application/json" }),
    };

    const response = await axios.patch(
        `${API_BASE_URL}/api/users/${userId}/`,
        data,
        { headers }
    );

    return response.data;
}

// ===================== payment and wallet ========================
export async function submitWalletInfo(userId, token, { wallet_number, payment_method }) {
    const headers = {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json"
    };

    const data = {
        wallet_number,
        payment_method
    };

    const response = await axios.patch(`${API_BASE_URL}/api/users/${userId}/`, data, { headers });
    return response.data;
}

// ==================Change Password========================
export async function changeInstructorPassword(token, oldPassword, newPassword) {
    const headers = {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
    };

    const body = {
        current_password: oldPassword,
        new_password: newPassword,
    };

    const response = await axios.post(`${API_BASE_URL}/api/users/change-password/`, body, { headers });
    return response.data;
}
// =====================Payment========================
// export async function createPayment(data, token) {
//     const headers = {
//         Authorization: `Bearer ${token}`,
//         "Content-Type": "application/json"
//     };

//     const response = await axios.post(`${API_BASE_URL}/api/payments/`, data, {
//         headers,
//     });

//     return response.data;
// }

// تعديل دفعة موجودة
// export async function updatePayment(paymentId, data, token) {
//     const headers = {
//         Authorization: `Bearer ${token}`,
//         "Content-Type": "application/json"
//     };

//     const response = await axios.patch(
//         `${API_BASE_URL}/api/payments/${paymentId}/`,
//         data,
//         { headers }
//     );

//     return response.data;
// }

// =====================Wallet and paymeny========================

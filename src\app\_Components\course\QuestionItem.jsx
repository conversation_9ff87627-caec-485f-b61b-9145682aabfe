// components/course/QuestionItem.jsx
"use client";
import React from "react";

export default function QuestionItem({ question, onEdit, onDelete }) {
  return (
    <div className="border rounded-lg p-4 bg-white">
      <div className="flex justify-between">
        <div>
          <p className="font-semibold text-gray-800">{question.text}</p>
          <ul className="mt-2 space-y-1 text-sm text-gray-700">
            {question.answers.map((answer) => (
              <li key={answer.id}>
                <span className={answer.is_correct ? "text-green-600 font-bold" : ""}>
                  - {answer.text}
                </span>
              </li>
            ))}
          </ul>
        </div>
        <div className="flex gap-2">
          <button
            onClick={onEdit}
            className="text-blue-600 hover:underline text-sm"
          >
            تعديل
          </button>
          <button
            onClick={onDelete}
            className="text-red-600 hover:underline text-sm"
          >
            حذف
          </button>
        </div>
      </div>
    </div>
  );
}

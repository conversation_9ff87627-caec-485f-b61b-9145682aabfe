// components/course/FileItem.jsx
"use client";
import React from "react";

export default function FileItem({ file, onDelete }) {
  return (
    <div className="flex items-center justify-between p-3 border rounded-lg bg-white">
      <div className="truncate text-sm text-gray-700">
        📄 {file.file_name || file.file?.split("/").pop()}
      </div>
      <div className="flex gap-2">
        <a
          href={file.file}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 text-sm hover:underline"
        >
          عرض
        </a>
        <button
          onClick={() => onDelete(file.id)}
          className="text-red-600 text-sm hover:underline"
        >
          حذف
        </button>
      </div>
    </div>
  );
}

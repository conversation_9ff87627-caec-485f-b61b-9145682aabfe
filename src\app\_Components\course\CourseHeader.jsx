// components/course/CourseHeader.jsx
"use client";
import React from "react";
import Link from "next/link";

export default function CourseHeader({ courseId, courseData, publishing, onTogglePublish }) {
  return (
    <div className="flex justify-between items-center mb-6">
      <h1 className="text-3xl font-bold text-gray-800">تفاصيل الكورس</h1>
      <div className="flex gap-2">
        <Link
          href={`/instructor/dashboard/${courseId}/add-lesson`}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          إضافة درس جديد
        </Link>
        <button
          onClick={onTogglePublish}
          disabled={publishing}
          className={`px-6 py-2 rounded-lg font-semibold transition-colors ${
            courseData.is_published
              ? "bg-red-600 hover:bg-red-700 text-white"
              : "bg-green-600 hover:bg-green-700 text-white"
          }`}
        >
          {publishing
            ? "..."
            : courseData.is_published
            ? "إلغاء النشر"
            : "نشر"}
        </button>
      </div>
    </div>
  );
}

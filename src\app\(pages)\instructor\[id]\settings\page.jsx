"use client";
import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { fetchInstructorProfile } from "../../../../../services/instructor";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "../../../../../store/authSlice";
import {
  userDataChange,
  changeInstructorPassword,
} from "../../../../../services/anyUserDataChange";
import InstructorPayment from "@/app/_Components/InstructorPayment/InstructorPayment";

export default function InstructorSettings() {
  const { id } = useParams();
  const router = useRouter();
  const user = useSelector(selectCurrentUser);
  const [form, setForm] = useState({
    username: "",
    email: "",
    bio: "",
    date_of_birth: "",
    first_name: "",
    last_name: "",
    profile_image: null,
  });
  const [passwordForm, setPasswordForm] = useState({
    old_password: "",
    new_password: "",
    confirm_password: "",
  });
  const [isInstructor, setIsInstructor] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [passwordError, setPasswordError] = useState(null);
  const [passwordSuccess, setPasswordSuccess] = useState(null);
  const [instructorProfileId, setInstructorProfileId] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  useEffect(() => {
    if (user && user.id && String(user.id) !== String(id)) {
      router.replace("/Notfound");
      return;
    }
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      const token = Cookies.get("authToken");
      if (!token) {
        setError("يرجى تسجيل الدخول");
        setLoading(false);
        return;
      }
      try {
        const res = await fetchInstructorProfile(id, token);
        console.log("Fetched instructor profile:", res);
        setForm({
          username: res.username || "",
          email: res.email || "",
          bio: res.bio || "",
          date_of_birth: res.date_of_birth || "",
          first_name: res.first_name || "",
          last_name: res.last_name || "",
          profile_image: null, // Initialize as null for file input
        });
        setImagePreview(res.profile_image || null); // Set preview to fetched image URL
        setIsInstructor(res.is_instructor || false);

        setInstructorProfileId(res.instructor_profile_id || null);
      } catch (err) {
        setError("حدث خطأ أثناء جلب البيانات");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [id, user]);

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    if (name === "profile_image" && files && files[0]) {
      setForm({ ...form, profile_image: files[0] });
      setImagePreview(URL.createObjectURL(files[0]));
    } else {
      setForm({ ...form, [name]: value });
    }
  };

  const handlePasswordChange = (e) => {
    setPasswordForm({ ...passwordForm, [e.target.name]: e.target.value });
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setPasswordError(null);
    setPasswordSuccess(null);
    if (passwordForm.new_password !== passwordForm.confirm_password) {
      setPasswordError("كلمتا المرور الجديدتان غير متطابقتين");
      return;
    }
    if (passwordForm.new_password.length < 8) {
      setPasswordError("يجب أن تكون كلمة المرور الجديدة 8 أحرف على الأقل");
      return;
    }
    const token = Cookies.get("authToken");
    if (!token) {
      setPasswordError("يرجى تسجيل الدخول");
      return;
    }
    try {
      await changeInstructorPassword(
        token,
        passwordForm.old_password,
        passwordForm.new_password
      );
      setPasswordSuccess("تم تغيير كلمة المرور بنجاح");
      setPasswordForm({
        old_password: "",
        new_password: "",
        confirm_password: "",
      });
      setShowPasswordForm(false);
    } catch (err) {
      setPasswordError("حدث خطأ أثناء تغيير كلمة المرور");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول");
      setSaving(false);
      return;
    }

    try {
      const formData = new FormData();
      formData.append("username", form.username);
      formData.append("email", form.email);
      formData.append("bio", form.bio);
      formData.append("date_of_birth", form.date_of_birth);
      formData.append("first_name", form.first_name);
      formData.append("last_name", form.last_name);
      if (form.profile_image) {
        formData.append("profile_image", form.profile_image);
      }

      await userDataChange(id, token, formData, true);

      setSuccess("تم حفظ التعديلات بنجاح");
      setTimeout(() => {
        router.push(`/instructor/dashboard/`);
      }, 2000);
    } catch (err) {
      setError("حدث خطأ أثناء حفظ البيانات");
    } finally {
      setSaving(false);
    }
  };
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        جاري التحميل...
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen pt-16">
        <div className="container mx-auto px-4 py-8 max-w-xl">
          <h1 className="text-2xl font-bold mb-6 text-center">
            إعدادات الحساب
          </h1>
          {error && (
            <div className="bg-red-100 text-red-700 p-3 rounded mb-4">
              {error}
            </div>
          )}
          {success && (
            <div className="bg-green-100 text-green-700 p-3 rounded mb-4">
              {success}
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block mb-1 font-medium">اسم المستخدم</label>
              <input
                type="text"
                name="username"
                value={form.username}
                onChange={handleChange}
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block mb-1 font-medium">
                البريد الإلكتروني
              </label>
              <input
                type="email"
                name="email"
                value={form.email}
                onChange={handleChange}
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block mb-1 font-medium">الاسم الاول</label>
              <input
                type="text"
                name="first_name"
                value={form.first_name}
                onChange={handleChange}
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block mb-1 font-medium">الاسم الثانى</label>
              <input
                type="text"
                name="last_name"
                value={form.last_name}
                onChange={handleChange}
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block mb-1 font-medium">نبذة تعريفية</label>
              <input
                type="text"
                name="bio"
                value={form.bio}
                onChange={handleChange}
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block mb-1 font-medium">تاريخ الميلاد</label>
              <input
                type="date"
                name="date_of_birth"
                value={form.date_of_birth}
                onChange={handleChange}
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block mb-1 font-medium">صورة شخصية</label>
              {imagePreview && (
                <div className="mb-2">
                  <img
                    src={imagePreview}
                    alt="Profile Preview"
                    className="w-32 h-32 object-cover rounded-full"
                  />
                </div>
              )}
              <input
                type="file"
                name="profile_image"
                onChange={handleChange}
                accept="image/*"
                className="w-full border rounded px-3 py-2"
              />
            </div>

            <button
              type="submit"
              className="w-full bg-primary text-white py-2 rounded-lg font-bold hover:bg-primary/90 transition"
              disabled={saving}
            >
              {saving ? "جارٍ الحفظ..." : "حفظ التعديلات"}
            </button>
          </form>
          <div className="mt-8 border-t pt-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="font-bold">تغيير كلمة المرور</h2>
              <button
                type="button"
                onClick={() => setShowPasswordForm(!showPasswordForm)}
                className="text-primary hover:text-primary/80"
              >
                {showPasswordForm ? "إلغاء" : "تغيير كلمة المرور"}
              </button>
            </div>
            {showPasswordForm && (
              <form onSubmit={handlePasswordSubmit} className="space-y-4">
                {passwordError && (
                  <div className="bg-red-100 text-red-700 p-3 rounded">
                    {passwordError}
                  </div>
                )}
                {passwordSuccess && (
                  <div className="bg-green-100 text-green-700 p-3 rounded">
                    {passwordSuccess}
                  </div>
                )}
                <div>
                  <label className="block mb-1">كلمة المرور القديمة</label>
                  <input
                    type="password"
                    name="old_password"
                    value={passwordForm.old_password}
                    onChange={handlePasswordChange}
                    className="w-full border rounded px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block mb-1">كلمة المرور الجديدة</label>
                  <input
                    type="password"
                    name="new_password"
                    value={passwordForm.new_password}
                    onChange={handlePasswordChange}
                    className="w-full border rounded px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block mb-1">
                    تأكيد كلمة المرور الجديدة
                  </label>
                  <input
                    type="password"
                    name="confirm_password"
                    value={passwordForm.confirm_password}
                    onChange={handlePasswordChange}
                    className="w-full border rounded px-3 py-2"
                    required
                  />
                </div>
                <button
                  type="submit"
                  className="w-full bg-primary text-white py-2 rounded-lg font-bold hover:bg-primary/90 transition"
                >
                  تغيير كلمة المرور
                </button>
              </form>
            )}
          </div>
        </div>
        <div className="container mx-auto px-4 py-8 max-w-xl">
          <InstructorPayment />
        </div>
      </div>
    </>
  );
}

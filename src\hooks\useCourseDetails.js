// hooks/useCourseDetails.js
"use client";
import { useEffect, useState, useCallback } from "react";
import { fetchInstructorCourse, fetchInstructorLessons } from "@/services/instructor";
import Cookies from "js-cookie";
import axios from "axios";

const API = process.env.NEXT_PUBLIC_API_URL;

export default function useCourseDetails(courseId) {
  const [state, setState] = useState({
    courseData: null,
    lessons: [],
    loading: true,
    error: null,
    publishing: false,
    expandedLesson: null,
    reviewComments: {},
    commentText: {},
    showImageCropper: {},
    imagePreview: {},
    showQuizForm: {},
    quizForm: {},
    showFileForm: {},
    uploadProgress: {},
    uploadSuccess: {},
    uploadError: {},
    isUploading: {},
    deletingResource: {},
    showQuestionForm: {},
    questionForm: {},
    questionEditForm: null,
    quizEditForm: null,
    questionLoading: {},
    answersForm: {},
    questionImages: {},
    croppedImages: {},
  });

  // دوال مباشرة بدون handlers منفصلة
  const refreshLessons = useCallback(async () => {
    const token = Cookies.get("authToken");
    const lessonsList = await fetchInstructorLessons(courseId, token);
    setState((prev) => ({ ...prev, lessons: lessonsList }));
  }, [courseId]);

  const handleAddQuiz = useCallback(async (lessonId, quizType) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setState((prev) => ({ ...prev, error: "يرجى تسجيل الدخول أولاً" }));
      return;
    }

    try {
      const quizData = {
        lesson: lessonId,
        title: state.quizForm[lessonId]?.title || "",
        description: state.quizForm[lessonId]?.description || "",
        passing_score: state.quizForm[lessonId]?.passing_score || 70,
        time_limit: state.quizForm[lessonId]?.time_limit || 0,
        quiz_type: quizType,
      };

      await axios.post(`${API}/api/quizzes/`, quizData, {
        headers: { Authorization: `Bearer ${token}` },
      });

      setState((prev) => ({
        ...prev,
        showQuizForm: { ...prev.showQuizForm, [lessonId]: false },
        quizForm: { ...prev.quizForm, [lessonId]: {} },
      }));
      await refreshLessons();
    } catch (error) {
      setState((prev) => ({ ...prev, error: "فشل في إضافة الاختبار/الواجب" }));
    }
  }, [state.quizForm, refreshLessons]);

  const handleDeleteQuiz = useCallback(async (quizId) => {
    if (!window.confirm("هل أنت متأكد من حذف هذا الامتحان/الواجب؟")) return;
    const token = Cookies.get("authToken");
    try {
      await axios.delete(`${API}/api/quizzes/${quizId}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      await refreshLessons();
    } catch {
      setState((prev) => ({ ...prev, error: "فشل في حذف الامتحان/الواجب" }));
    }
  }, [refreshLessons]);

  const handleAddQuestionWithAnswers = useCallback(async (quizId) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setState((prev) => ({ ...prev, error: "يرجى تسجيل الدخول أولاً" }));
      return;
    }

    setState((prev) => ({ ...prev, questionLoading: { ...prev.questionLoading, [quizId]: true } }));

    try {
      const quizObj = state.lessons.flatMap(l => l.quizzes || []).find(q => q.id === quizId);
      let qType = state.questionForm[quizId]?.question_type || "mcq";
      if (qType === "mcq") qType = "multiple_choice";

      let points = 1;
      if (quizObj && quizObj.quiz_type === 'exam') {
        points = Number(state.questionForm[quizId]?.points) || 1;
      }

      const formData = new FormData();
      formData.append('quiz', quizId);
      formData.append('text', state.questionForm[quizId]?.text || "");
      formData.append('question_type', qType);
      formData.append('points', points);
      formData.append('order', Number(state.questionForm[quizId]?.order) || 1);

      if (state.croppedImages[quizId]) {
        formData.append('image', state.croppedImages[quizId], 'question-image.jpg');
      }

      const qRes = await axios.post(`${API}/api/questions/`, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      const questionId = qRes.data.id;

      if ((qType === 'multiple_choice' || qType === 'true_false') && state.answersForm[quizId]?.length > 0) {
        for (const ans of state.answersForm[quizId]) {
          await axios.post(`${API}/api/answers/`, {
            question: questionId,
            text: ans.text,
            is_correct: ans.is_correct,
          }, { headers: { Authorization: `Bearer ${token}` } });
        }
      }

      setState((prev) => ({
        ...prev,
        showQuestionForm: { ...prev.showQuestionForm, [quizId]: false },
        questionForm: { ...prev.questionForm, [quizId]: {} },
        answersForm: { ...prev.answersForm, [quizId]: [] },
        croppedImages: { ...prev.croppedImages, [quizId]: null },
        imagePreview: { ...prev.imagePreview, [quizId]: null },
      }));

      await refreshLessons();
    } catch (err) {
      setState((prev) => ({ ...prev, error: "فشل في إضافة السؤال أو الإجابات" }));
    } finally {
      setState((prev) => ({ ...prev, questionLoading: { ...prev.questionLoading, [quizId]: false } }));
    }
  }, [state.lessons, state.questionForm, state.answersForm, state.croppedImages, refreshLessons]);

  const handleDeleteQuestion = useCallback(async (quizId, questionId) => {
    if (!window.confirm("هل أنت متأكد من حذف هذا السؤال؟")) return;
    const token = Cookies.get("authToken");

    setState((prev) => ({ ...prev, questionLoading: { ...prev.questionLoading, [quizId]: true } }));

    try {
      await axios.delete(`${API}/api/questions/${questionId}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      await refreshLessons();
      setState((prev) => ({ ...prev, questionEditForm: null }));
    } catch {
      setState((prev) => ({ ...prev, error: "فشل في حذف السؤال" }));
    } finally {
      setState((prev) => ({ ...prev, questionLoading: { ...prev.questionLoading, [quizId]: false } }));
    }
  }, [refreshLessons]);

  const handleEditQuestion = useCallback(async (quizId, questionId) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setState((prev) => ({ ...prev, error: "يرجى تسجيل الدخول أولاً" }));
      return;
    }

    setState((prev) => ({ ...prev, questionLoading: { ...prev.questionLoading, [quizId]: true } }));

    try {
      let qType = state.questionEditForm.question.question_type;
      if (qType === "mcq") qType = "multiple_choice";

      const formData = new FormData();
      formData.append('text', state.questionEditForm.question.text);
      formData.append('question_type', qType);
      formData.append('points', Number(state.questionEditForm.question.points) || 1);
      formData.append('order', Number(state.questionEditForm.question.order) || 1);

      if (state.croppedImages[`edit_${questionId}`]) {
        formData.append('image', state.croppedImages[`edit_${questionId}`], 'question-image.jpg');
      }

      await axios.patch(`${API}/api/questions/${questionId}/`, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      setState((prev) => ({ ...prev, questionEditForm: null }));
      await refreshLessons();
    } catch (err) {
      setState((prev) => ({ ...prev, error: "فشل في تعديل السؤال" }));
    } finally {
      setState((prev) => ({ ...prev, questionLoading: { ...prev.questionLoading, [quizId]: false } }));
    }
  }, [state.questionEditForm, state.croppedImages, refreshLessons]);

  const handleImageSelect = useCallback((quizId, file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      setState((prev) => ({
        ...prev,
        imagePreview: { ...prev.imagePreview, [quizId]: e.target.result },
        questionImages: { ...prev.questionImages, [quizId]: file },
        showImageCropper: { ...prev.showImageCropper, [quizId]: true },
      }));
    };
    reader.readAsDataURL(file);
  }, []);

  const removeQuestionImage = useCallback((quizId) => {
    setState((prev) => ({
      ...prev,
      questionImages: { ...prev.questionImages, [quizId]: null },
      imagePreview: { ...prev.imagePreview, [quizId]: null },
      croppedImages: { ...prev.croppedImages, [quizId]: null },
      showImageCropper: { ...prev.showImageCropper, [quizId]: false },
    }));
  }, []);

  const handlers = {
    // State setters
    setShowQuizForm: useCallback((value) => {
      setState((prev) => ({
        ...prev,
        showQuizForm: typeof value === 'function' ? value(prev.showQuizForm) : value
      }));
    }, []),

    setQuizForm: useCallback((value) => {
      setState((prev) => ({
        ...prev,
        quizForm: typeof value === 'function' ? value(prev.quizForm) : value
      }));
    }, []),

    setExpandedLesson: useCallback((id) => {
      setState((prev) => ({ ...prev, expandedLesson: id }));
    }, []),

    clearError: useCallback(() => {
      setState((prev) => ({ ...prev, error: null }));
    }, []),

    // Question form setters
    setShowQuestionForm: useCallback((value) => {
      setState((prev) => ({
        ...prev,
        showQuestionForm: typeof value === 'function' ? value(prev.showQuestionForm) : value
      }));
    }, []),

    setQuestionForm: useCallback((value) => {
      setState((prev) => ({
        ...prev,
        questionForm: typeof value === 'function' ? value(prev.questionForm) : value
      }));
    }, []),

    setAnswersForm: useCallback((value) => {
      setState((prev) => ({
        ...prev,
        answersForm: typeof value === 'function' ? value(prev.answersForm) : value
      }));
    }, []),

    setQuestionEditForm: useCallback((value) => {
      setState((prev) => ({ ...prev, questionEditForm: value }));
    }, []),

    setImagePreview: useCallback((value) => {
      setState((prev) => ({
        ...prev,
        imagePreview: typeof value === 'function' ? value(prev.imagePreview) : value
      }));
    }, []),

    setCroppedImages: useCallback((value) => {
      setState((prev) => ({
        ...prev,
        croppedImages: typeof value === 'function' ? value(prev.croppedImages) : value
      }));
    }, []),

    setShowImageCropper: useCallback((value) => {
      setState((prev) => ({
        ...prev,
        showImageCropper: typeof value === 'function' ? value(prev.showImageCropper) : value
      }));
    }, []),

    // Actions
    handleAddQuiz,
    handleDeleteQuiz,
    handleAddQuestionWithAnswers,
    handleDeleteQuestion,
    handleEditQuestion,
    handleImageSelect,
    removeQuestionImage,
    refreshLessons,

    togglePublish: useCallback(async () => {
      const token = Cookies.get("authToken");
      setState((prev) => ({ ...prev, publishing: true }));
      try {
        const formData = new FormData();
        formData.append("is_published", !state.courseData.is_published);
        await axios.patch(`${API}/api/courses/${courseId}/`, formData, {
          headers: { Authorization: `Bearer ${token}` },
        });
        const course = await fetchInstructorCourse(courseId, token);
        setState((prev) => ({ ...prev, courseData: course, publishing: false }));
      } catch {
        setState((prev) => ({ ...prev, error: "فشل في تحديث حالة النشر", publishing: false }));
      }
    }, [courseId, state.courseData?.is_published]),
  };



  useEffect(() => {
    const token = Cookies.get("authToken");
    if (!token) {
      setState((prev) => ({ ...prev, error: "لم يتم تسجيل الدخول.", loading: false }));
      return;
    }

    Promise.all([
      fetchInstructorCourse(courseId, token),
      fetchInstructorLessons(courseId, token),
    ])
      .then(([course, lessons]) => {
        setState((prev) => ({
          ...prev,
          courseData: course,
          lessons,
          loading: false,
        }));
      })
      .catch((error) => {
        setState((prev) => ({
          ...prev,
          error: error.message || "حدث خطأ أثناء تحميل البيانات",
          loading: false,
        }));
      });
  }, [courseId]);

  return {
    ...state,
    handlers,
    states: state
  };
}

// components/course/CourseInfoSection.jsx
"use client";
import React from "react";

export default function CourseInfoSection({ courseData }) {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-700 mb-2">معلومات الكورس</h2>
      <div className="bg-gray-50 p-4 rounded-lg">
        <p className="text-gray-600">
          <span className="font-medium">عنوان الكورس:</span> {courseData.title}
        </p>
        <p className="text-gray-600">
          <span className="font-medium">الوصف:</span> {courseData.description}
        </p>
        <p className="text-gray-600">
          <span className="font-medium">الفئة:</span> {courseData.category?.name}
        </p>
        <p className="text-gray-600">
          <span className="font-medium">المستوى:</span> {courseData.level}
        </p>
        <p className="text-gray-600">
          <span className="font-medium">اللغة:</span> {courseData.language}
        </p>
      </div>
    </div>
  );
}

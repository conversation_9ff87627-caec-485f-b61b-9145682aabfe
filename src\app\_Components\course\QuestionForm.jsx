// components/course/QuestionForm.jsx
"use client";
import React, { useState } from "react";

export default function QuestionForm({ question, onSave, onDelete }) {
  const [form, setForm] = useState({
    text: question?.text || "",
    answers: question?.answers?.map((a) => a.text) || ["", "", "", ""],
    correctAnswer: question?.answers?.findIndex((a) => a.is_correct) ?? 0,
  });

  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleAnswerChange = (index, value) => {
    const updatedAnswers = [...form.answers];
    updatedAnswers[index] = value;
    setForm((prev) => ({ ...prev, answers: updatedAnswers }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const payload = {
        text: form.text,
        answers: form.answers.map((answerText, idx) => ({
          text: answerText,
          is_correct: idx === form.correctAnswer,
        })),
      };
      await onSave(payload);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700">نص السؤال</label>
        <input
          type="text"
          name="text"
          value={form.text}
          onChange={handleChange}
          required
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
        />
      </div>
      <div className="space-y-2">
        {form.answers.map((answer, index) => (
          <div key={index} className="flex items-center gap-2">
            <input
              type="radio"
              name="correctAnswer"
              checked={form.correctAnswer === index}
              onChange={() => setForm((prev) => ({ ...prev, correctAnswer: index }))}
            />
            <input
              type="text"
              value={answer}
              onChange={(e) => handleAnswerChange(index, e.target.value)}
              placeholder={`إجابة ${index + 1}`}
              className="flex-1 border border-gray-300 rounded-md px-3 py-1"
              required
            />
          </div>
        ))}
      </div>
      <div className="flex gap-2">
        <button
          type="submit"
          disabled={loading}
          className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
        >
          {question ? "تحديث" : "إضافة"}
        </button>
        {question && (
          <button
            type="button"
            onClick={onDelete}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            حذف
          </button>
        )}
      </div>
    </form>
  );
}
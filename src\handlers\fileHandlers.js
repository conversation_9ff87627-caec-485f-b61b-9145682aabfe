// handlers/fileHandlers.js
import axios from "axios";
import Cookies from "js-cookie";

const API = process.env.NEXT_PUBLIC_API_URL;

export function initFileHandlers({ setState, getState }) {
  const token = Cookies.get("authToken");

  const uploadFile = async (formData) => {
    const res = await axios.post(`${API}/api/resources/`, formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "multipart/form-data",
      },
    });
    return res.data;
  };

  const deleteFile = async (fileId) => {
    await axios.delete(`${API}/api/resources/${fileId}/`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  };

  return {
    uploadFile,
    deleteFile,
  };
}

// components/course/QuizForm.jsx
"use client";
import React, { useState } from "react";

export default function QuizForm({ quiz, lessonId, onSave, onDelete }) {
  const [form, setForm] = useState({
    title: quiz?.title || "",
    description: quiz?.description || "",
  });

  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onSave(form);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700">عنوان الكويز</label>
        <input
          type="text"
          name="title"
          value={form.title}
          onChange={handleChange}
          required
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">الوصف</label>
        <textarea
          name="description"
          value={form.description}
          onChange={handleChange}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2"
        />
      </div>
      <div className="flex gap-2">
        <button
          type="submit"
          disabled={loading}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          {quiz ? "تحديث" : "إضافة"}
        </button>
        {quiz && (
          <button
            type="button"
            onClick={() => onDelete()}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            حذف
          </button>
        )}
      </div>
    </form>
  );
}
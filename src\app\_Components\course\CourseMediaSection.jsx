// components/course/CourseMediaSection.jsx
"use client";
import React from "react";

export default function CourseMediaSection({ courseData }) {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-700 mb-2">الوسائط</h2>
      <div className="bg-gray-50 p-4 rounded-lg">
        {/* الصورة المصغرة */}
        <div className="mb-4">
          <p className="font-medium text-gray-700 mb-2">الصورة المصغرة:</p>
          <img
            src={`https://res.cloudinary.com/di5y7hnub/${courseData.thumbnail}`}
            alt="Course thumbnail"
            className="w-full h-48 object-cover rounded-lg"
          />
        </div>
        {/* فيديو العرض */}
        <div>
          <p className="font-medium text-gray-700 mb-2">فيديو العرض:</p>
          <video controls className="w-full rounded-lg">
            <source
              src={`https://res.cloudinary.com/di5y7hnub/${courseData.promo_video}`}
              type="video/mp4"
            />
            Your browser does not support the video tag.
          </video>
        </div>
      </div>
    </div>
  );
}

// components/course/LessonsSection.jsx
"use client";
import React from "react";
import LessonItem from "./LessonItem";

export default function LessonsSection({ courseId, lessons, handlers, states }) {
  return (
    <div className="mt-8">
      <h2 className="text-2xl font-bold text-gray-800 mb-4">الدروس</h2>
      {lessons.length > 0 ? (
        <div className="space-y-4">
          {lessons.map((lesson) => (
            <LessonItem
              key={lesson.id}
              lesson={lesson}
              courseId={courseId}
              handlers={handlers}
              states={states}
            />
          ))}
        </div>
      ) : (
        <div className="text-gray-400 text-sm">لا يوجد دروس بعد</div>
      )}
    </div>
  );
}

// components/course/LessonItem.jsx
"use client";
import React from "react";

export default function LessonItem({ lesson, courseId, handlers, states }) {
  const showQuizForm = states.showQuizForm[lesson.id];
  const quizForm = states.quizForm[lesson.id] || {};

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-semibold">
            {lesson.order}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800">{lesson.title}</h3>
            <p className="text-sm text-gray-500">
              {lesson.lesson_type === "video" ? "درس فيديو" : "درس نصي"}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handlers.handleSeeLesson(lesson.id)}
            className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg"
          >👁 عرض</button>

          <button
            onClick={() =>
              handlers.setExpandedLesson(
                states.expandedLesson === lesson.id ? null : lesson.id
              )
            }
            className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg"
          >⏷</button>

          <button
            onClick={() => handlers.handleDeleteLesson(lesson.id)}
            className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
          >🗑 حذف</button>
        </div>
      </div>

      {states.expandedLesson === lesson.id && (
        <div className="mt-4 bg-gray-50 rounded-lg p-4 border">
          {/* أزرار الإضافة */}
          <div className="flex gap-2 mb-4">
            <button
              className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
              onClick={() => handlers.setShowQuizForm(prev => ({ ...prev, [lesson.id]: 'exam' }))}
            >
              إضافة امتحان
            </button>
            <button
              className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600"
              onClick={() => handlers.setShowQuizForm(prev => ({ ...prev, [lesson.id]: 'assignment' }))}
            >
              إضافة واجب
            </button>
            {!lesson.resources && (
              <button
                className="bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600"
                onClick={() => handlers.setShowFileForm(prev => ({ ...prev, [lesson.id]: true }))}
              >
                رفع ملف
              </button>
            )}
          </div>

          {/* فورم إضافة اختبار أو واجب */}
          {showQuizForm && (
            <form
              className="mb-4 bg-white p-3 rounded shadow flex flex-col gap-2"
              onSubmit={e => {
                e.preventDefault();
                handlers.handleAddQuiz(lesson.id, showQuizForm);
              }}
            >
              <input
                type="text"
                className="border rounded px-2 py-1"
                placeholder={`عنوان ${showQuizForm === 'exam' ? 'الامتحان' : 'الواجب'}`}
                value={quizForm.title || ''}
                onChange={e => handlers.setQuizForm(prev => ({ ...prev, [lesson.id]: { ...prev[lesson.id], title: e.target.value } }))}
                required
              />
              <textarea
                className="border rounded px-2 py-1"
                placeholder={`وصف ${showQuizForm === 'exam' ? 'الامتحان' : 'الواجب'}`}
                value={quizForm.description || ''}
                onChange={e => handlers.setQuizForm(prev => ({ ...prev, [lesson.id]: { ...prev[lesson.id], description: e.target.value } }))}
              />
              {showQuizForm === 'exam' && (
                <input
                  type="number"
                  className="border rounded px-2 py-1"
                  placeholder="درجة النجاح (افتراضي 70)"
                  value={quizForm.passing_score || ''}
                  onChange={e => handlers.setQuizForm(prev => ({ ...prev, [lesson.id]: { ...prev[lesson.id], passing_score: e.target.value } }))}
                />
              )}
              {showQuizForm === 'exam' && (
                <input
                  type="number"
                  className="border rounded px-2 py-1"
                  placeholder="الوقت بالدقائق (0 = بدون حد)"
                  value={quizForm.time_limit || ''}
                  onChange={e => handlers.setQuizForm(prev => ({ ...prev, [lesson.id]: { ...prev[lesson.id], time_limit: e.target.value } }))}
                />
              )}
              <div className="flex gap-2">
                <button type="submit" className="bg-blue-600 text-white px-4 py-1 rounded">حفظ</button>
                <button type="button" className="bg-gray-300 px-4 py-1 rounded" onClick={() => handlers.setShowQuizForm(prev => ({ ...prev, [lesson.id]: false }))}>إلغاء</button>
              </div>
            </form>
          )}

          {/* فورم رفع ملف */}
          {states.showFileForm[lesson.id] && (
            <form
              className="mb-4 bg-white p-3 rounded shadow flex flex-col gap-2"
              onSubmit={e => {
                e.preventDefault();
                const fileInput = e.target.querySelector('input[type="file"]');
                const file = fileInput?.files[0];
                console.log("📄 Selected file:", file);
                if (file) {
                  handlers.handleAddFile(lesson.id, file);
                } else {
                  console.error("❌ No file selected");
                }
              }}
            >
              {!states.uploadedFile?.[lesson.id] && (
                <>
                  <input
                    type="file"
                    className="border rounded px-2 py-1 cursor-pointer"
                    accept="application/pdf"
                    required
                  />
                  {states.isUploading[lesson.id] && (
                    <div className="w-full bg-gray-200 rounded h-3 mt-2 overflow-hidden">
                      <div
                        className="bg-blue-500 h-full transition-all"
                        style={{ width: `${states.uploadProgress[lesson.id] || 0}%` }}
                      ></div>
                    </div>
                  )}
                  <div className="flex gap-2">
                    <button
                      type="submit"
                      className="bg-blue-600 text-white px-4 py-1 rounded disabled:opacity-50"
                      disabled={states.isUploading[lesson.id]}
                    >
                      {states.isUploading[lesson.id] ? "جارٍ الرفع..." : "رفع"}
                    </button>
                    <button
                      type="button"
                      className="bg-gray-300 px-4 py-1 rounded"
                      onClick={() => handlers.setShowFileForm(prev => ({ ...prev, [lesson.id]: false }))}
                      disabled={states.isUploading[lesson.id]}
                    >
                      إلغاء
                    </button>
                  </div>
                </>
              )}
            </form>
          )}

          {/* عرض الملف المرفق */}
          {lesson.resources && (
            <div className="mt-1">
              <a
                href={lesson.resources}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline text-sm"
              >
                عرض الملف المرفق 📄
              </a>
              {states.deletingResource[lesson.id] ? (
                <span className="ml-2 inline-block align-middle">
                  <svg className="animate-spin h-5 w-5 text-red-600" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z" />
                  </svg>
                  <span className="text-xs text-gray-500 ml-1">جاري الحذف...</span>
                </span>
              ) : (
                <button
                  onClick={() => handlers.handleDeleteResource(lesson.id)}
                  className="text-red-600 hover:underline ml-2"
                  disabled={states.deletingResource[lesson.id]}
                >
                  حذف
                </button>
              )}
            </div>
          )}

          {/* عرض قائمة الامتحانات والواجبات */}
          <div className="mt-4">
            <h4 className="font-bold text-gray-700 mb-2">الامتحانات والواجبات</h4>
            {lesson.quizzes && lesson.quizzes.length > 0 ? (
              <ul className="space-y-2">
                {lesson.quizzes.map((quiz) => (
                  <li key={quiz.id} className="bg-white border rounded p-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-semibold text-blue-700">{quiz.quiz_type === 'exam' ? 'امتحان' : 'واجب'}:</span> {quiz.title}
                        <div className="text-sm text-gray-600">{quiz.description}</div>
                        <div className="text-xs text-gray-500">درجة النجاح: {quiz.passing_score} | الوقت: {quiz.time_limit} دقيقة</div>
                      </div>
                      <div className="flex gap-2">
                        <button
                          className="text-blue-600 hover:underline text-sm"
                          onClick={() => {
                            handlers.setQuizEditForm({ quiz, lessonId: lesson.id });
                          }}
                        >
                          تعديل
                        </button>
                        <button
                          className="text-red-600 hover:underline text-sm"
                          onClick={() => handlers.handleDeleteQuiz(quiz.id)}
                        >
                          حذف
                        </button>
                        <button
                          className="text-green-600 hover:underline text-sm"
                          onClick={() => {
                            handlers.setShowQuestionForm(prev => ({ ...prev, [quiz.id]: !prev[quiz.id] }));
                            if (!states.showQuestionForm[quiz.id]) {
                              handlers.setAnswersForm(prev => ({
                                ...prev,
                                [quiz.id]: [
                                  { text: '', is_correct: false },
                                  { text: '', is_correct: false },
                                ]
                              }));
                              handlers.setQuestionForm(prev => ({
                                ...prev,
                                [quiz.id]: {
                                  ...prev[quiz.id],
                                  question_type: 'mcq'
                                }
                              }));
                            }
                          }}
                        >
                          {states.showQuestionForm[quiz.id] ? 'إغلاق' : 'إضافة سؤال'}
                        </button>
                      </div>
                    </div>

                    {/* عرض الأسئلة الموجودة */}
                    {quiz.questions && quiz.questions.length > 0 && (
                      <div className="mt-3 border-t pt-3">
                        <h5 className="font-medium text-gray-700 mb-2">الأسئلة:</h5>
                        <div className="space-y-2">
                          {quiz.questions.map((question, qIndex) => (
                            <div key={question.id} className="bg-gray-50 p-2 rounded text-sm">
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <span className="font-medium">س{qIndex + 1}:</span> {question.text}
                                  <div className="text-xs text-gray-500 mt-1">
                                    النوع: {question.question_type === 'multiple_choice' ? 'اختيار متعدد' :
                                           question.question_type === 'true_false' ? 'صح/خطأ' : 'نصي'} |
                                    النقاط: {question.points}
                                    {question.image_url && (
                                      <span className="text-blue-500 ml-2">📷 يحتوي على صورة</span>
                                    )}
                                  </div>

                                  {/* عرض الصورة إذا كانت موجودة */}
                                  {question.image_url && (
                                    <div className="mt-2">
                                      <img
                                        src={question.image_url}
                                        alt="صورة السؤال"
                                        className="max-w-full h-24 object-contain border rounded"
                                      />
                                    </div>
                                  )}
                                  {question.answers && question.answers.length > 0 && (
                                    <div className="mt-1">
                                      <span className="text-xs text-gray-600">الإجابات:</span>
                                      <ul className="text-xs text-gray-600 ml-4">
                                        {question.answers.map((answer, aIndex) => (
                                          <li key={answer.id} className={answer.is_correct ? 'text-green-600 font-medium' : ''}>
                                            {aIndex + 1}. {answer.text} {answer.is_correct && '✓'}
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}
                                </div>
                                <div className="flex gap-1 ml-2">
                                  <button
                                    className="text-blue-500 hover:underline text-xs"
                                    onClick={() => {
                                      handlers.setQuestionEditForm({
                                        question: question,
                                        answers: question.answers || [],
                                        quizId: quiz.id
                                      });
                                    }}
                                  >
                                    تعديل
                                  </button>
                                  <button
                                    className="text-red-500 hover:underline text-xs"
                                    onClick={() => {
                                      if (window.confirm("هل أنت متأكد من حذف هذا السؤال؟")) {
                                        handlers.handleDeleteQuestion && handlers.handleDeleteQuestion(quiz.id, question.id);
                                      }
                                    }}
                                  >
                                    حذف
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* فورم إضافة سؤال جديد */}
                    {states.showQuestionForm[quiz.id] && (
                      <div className="mt-3 border-t pt-3">
                        <form
                          className="bg-gray-50 p-3 rounded space-y-3"
                          onSubmit={e => {
                            e.preventDefault();
                            handlers.handleAddQuestionWithAnswers && handlers.handleAddQuestionWithAnswers(quiz.id);
                          }}
                        >
                          <h5 className="font-medium text-gray-700">إضافة سؤال جديد</h5>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">نص السؤال:</label>
                            <textarea
                              className="w-full border rounded px-2 py-1 text-sm"
                              placeholder="اكتب السؤال هنا..."
                              value={states.questionForm[quiz.id]?.text || ''}
                              onChange={e => handlers.setQuestionForm(prev => ({
                                ...prev,
                                [quiz.id]: { ...prev[quiz.id], text: e.target.value }
                              }))}
                              required
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">نوع السؤال:</label>
                            <select
                              className="w-full border rounded px-2 py-1 text-sm"
                              value={states.questionForm[quiz.id]?.question_type || 'mcq'}
                              onChange={e => {
                                const newType = e.target.value;
                                handlers.setQuestionForm(prev => ({
                                  ...prev,
                                  [quiz.id]: { ...prev[quiz.id], question_type: newType }
                                }));

                                // تحديث الإجابات حسب نوع السؤال
                                if (newType === 'true_false') {
                                  handlers.setAnswersForm(prev => ({
                                    ...prev,
                                    [quiz.id]: [
                                      { text: 'صحيح', is_correct: false },
                                      { text: 'خطأ', is_correct: false }
                                    ]
                                  }));
                                } else if (newType === 'mcq') {
                                  handlers.setAnswersForm(prev => ({
                                    ...prev,
                                    [quiz.id]: [
                                      { text: '', is_correct: false },
                                      { text: '', is_correct: false },
                                    ]
                                  }));
                                }
                              }}
                            >
                              <option value="mcq">اختيار متعدد</option>
                              <option value="true_false">صح/خطأ</option>
                            </select>
                          </div>

                          {quiz.quiz_type === 'exam' && (
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">النقاط:</label>
                              <input
                                type="number"
                                className="w-full border rounded px-2 py-1 text-sm"
                                placeholder="عدد النقاط"
                                min="1"
                                value={states.questionForm[quiz.id]?.points || ''}
                                onChange={e => handlers.setQuestionForm(prev => ({
                                  ...prev,
                                  [quiz.id]: { ...prev[quiz.id], points: e.target.value }
                                }))}
                              />
                            </div>
                          )}

                          {/* ترتيب السؤال */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">الترتيب:</label>
                            <input
                              type="number"
                              className="w-full border rounded px-2 py-1 text-sm"
                              placeholder="ترتيب السؤال"
                              value={states.questionForm[quiz.id]?.order || ''}
                              onChange={e => handlers.setQuestionForm(prev => ({
                                ...prev,
                                [quiz.id]: { ...prev[quiz.id], order: e.target.value }
                              }))}
                            />
                          </div>

                          {/* رفع الصور */}
                          <div className="border rounded p-2">
                            <label className="block text-sm font-bold mb-2">صورة السؤال (اختيارية)</label>
                            {!states.imagePreview[quiz.id] ? (
                              <input
                                type="file"
                                accept="image/*"
                                className="border rounded px-2 py-1 w-full"
                                onChange={e => {
                                  const file = e.target.files[0];
                                  if (file) handlers.handleImageSelect(quiz.id, file);
                                }}
                              />
                            ) : (
                              <div className="space-y-2">
                                <img
                                  src={states.croppedImages[quiz.id] ? URL.createObjectURL(states.croppedImages[quiz.id]) : states.imagePreview[quiz.id]}
                                  alt="معاينة الصورة"
                                  className="max-w-full h-32 object-contain border rounded"
                                />
                                <div className="flex gap-2">
                                  <button
                                    type="button"
                                    className="bg-red-500 text-white px-2 py-1 rounded text-xs"
                                    onClick={() => handlers.removeQuestionImage(quiz.id)}
                                  >
                                    حذف الصورة
                                  </button>
                                  <button
                                    type="button"
                                    className="bg-blue-500 text-white px-2 py-1 rounded text-xs"
                                    onClick={() => handlers.setShowImageCropper(prev => ({ ...prev, [quiz.id]: true }))}
                                  >
                                    تعديل الصورة
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* الإجابات */}
                          {(states.questionForm[quiz.id]?.question_type === 'mcq' || states.questionForm[quiz.id]?.question_type === 'true_false') && (
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">الإجابات:</label>
                              <div className="space-y-2">
                                {(states.answersForm[quiz.id] || []).map((answer, index) => (
                                  <div key={index} className="flex items-center gap-2">
                                    <input
                                      type="text"
                                      className="flex-1 border rounded px-2 py-1 text-sm"
                                      placeholder={`الإجابة ${index + 1}`}
                                      value={answer.text}
                                      onChange={e => {
                                        const newAnswers = [...(states.answersForm[quiz.id] || [])];
                                        newAnswers[index] = { ...newAnswers[index], text: e.target.value };
                                        handlers.setAnswersForm(prev => ({ ...prev, [quiz.id]: newAnswers }));
                                      }}
                                      disabled={states.questionForm[quiz.id]?.question_type === 'true_false'}
                                      required
                                    />
                                    <label className="flex items-center gap-1">
                                      <input
                                        type="radio"
                                        name={`correct_answer_${quiz.id}`}
                                        checked={answer.is_correct}
                                        onChange={() => {
                                          const newAnswers = (states.answersForm[quiz.id] || []).map((ans, i) => ({
                                            ...ans,
                                            is_correct: i === index
                                          }));
                                          handlers.setAnswersForm(prev => ({ ...prev, [quiz.id]: newAnswers }));
                                        }}
                                      />
                                      <span className="text-sm text-gray-600">صحيح</span>
                                    </label>
                                  </div>
                                ))}
                              </div>

                              {states.questionForm[quiz.id]?.question_type === 'mcq' && (
                                <div className="flex gap-2 mt-2">
                                  <button
                                    type="button"
                                    className="text-blue-600 hover:underline text-sm"
                                    onClick={() => {
                                      const currentAnswers = states.answersForm[quiz.id] || [];
                                      if (currentAnswers.length < 6) {
                                        handlers.setAnswersForm(prev => ({
                                          ...prev,
                                          [quiz.id]: [...currentAnswers, { text: '', is_correct: false }]
                                        }));
                                      }
                                    }}
                                  >
                                    إضافة إجابة
                                  </button>
                                  {(states.answersForm[quiz.id] || []).length > 2 && (
                                    <button
                                      type="button"
                                      className="text-red-600 hover:underline text-sm"
                                      onClick={() => {
                                        const currentAnswers = states.answersForm[quiz.id] || [];
                                        handlers.setAnswersForm(prev => ({
                                          ...prev,
                                          [quiz.id]: currentAnswers.slice(0, -1)
                                        }));
                                      }}
                                    >
                                      حذف آخر إجابة
                                    </button>
                                  )}
                                </div>
                              )}
                            </div>
                          )}

                          <div className="flex gap-2">
                            <button
                              type="submit"
                              className="bg-blue-600 text-white px-4 py-1 rounded text-sm hover:bg-blue-700"
                              disabled={states.questionLoading[quiz.id]}
                            >
                              {states.questionLoading[quiz.id] ? 'جاري الحفظ...' : 'حفظ السؤال'}
                            </button>
                            <button
                              type="button"
                              className="bg-gray-300 px-4 py-1 rounded text-sm hover:bg-gray-400"
                              onClick={() => handlers.setShowQuestionForm(prev => ({ ...prev, [quiz.id]: false }))}
                            >
                              إلغاء
                            </button>
                          </div>
                        </form>
                      </div>
                    )}
                  </li>
                ))}
              </ul>
            ) : (
              <div className="text-gray-400 text-sm">لا يوجد امتحانات أو واجبات بعد</div>
            )}
          </div>
        </div>
      )}

      {/* فورم تعديل سؤال */}
      {states.questionEditForm && states.questionEditForm.quizId &&
       lesson.quizzes?.some(quiz => quiz.id === states.questionEditForm.quizId) && (
        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-bold text-gray-700 mb-3">تعديل السؤال</h4>
          <form
            className="space-y-3"
            onSubmit={e => {
              e.preventDefault();
              handlers.handleEditQuestion(states.questionEditForm.quizId, states.questionEditForm.question.id);
            }}
          >
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">نص السؤال:</label>
              <textarea
                className="w-full border rounded px-2 py-1 text-sm"
                placeholder="نص السؤال"
                value={states.questionEditForm.question.text || ''}
                onChange={e => handlers.setQuestionEditForm(prev => ({
                  ...prev,
                  question: { ...prev.question, text: e.target.value }
                }))}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">نوع السؤال:</label>
              <select
                className="w-full border rounded px-2 py-1 text-sm"
                value={states.questionEditForm.question.question_type || 'mcq'}
                onChange={e => {
                  const newType = e.target.value;
                  handlers.setQuestionEditForm(prev => ({
                    ...prev,
                    question: { ...prev.question, question_type: newType },
                    answers: (newType === 'mcq' || newType === 'true_false')
                      ? (newType === 'true_false'
                          ? [
                              { text: 'صح', is_correct: false },
                              { text: 'خطأ', is_correct: false },
                            ]
                          : [
                              { text: '', is_correct: false },
                              { text: '', is_correct: false },
                            ])
                      : []
                  }));
                }}
              >
                <option value="mcq">اختيار من متعدد</option>
                <option value="true_false">صح أو خطأ</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">النقاط:</label>
              <input
                type="number"
                className="w-full border rounded px-2 py-1 text-sm"
                placeholder="النقاط"
                value={states.questionEditForm.question.points || ''}
                onChange={e => handlers.setQuestionEditForm(prev => ({
                  ...prev,
                  question: { ...prev.question, points: e.target.value }
                }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الترتيب:</label>
              <input
                type="number"
                className="w-full border rounded px-2 py-1 text-sm"
                placeholder="الترتيب"
                value={states.questionEditForm.question.order || ''}
                onChange={e => handlers.setQuestionEditForm(prev => ({
                  ...prev,
                  question: { ...prev.question, order: e.target.value }
                }))}
              />
            </div>

            {/* الإجابات للتعديل */}
            {(states.questionEditForm.question.question_type === 'mcq' || states.questionEditForm.question.question_type === 'true_false') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الإجابات:</label>
                <div className="space-y-2">
                  {(states.questionEditForm.answers || []).map((answer, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <input
                        type="text"
                        className="flex-1 border rounded px-2 py-1 text-sm"
                        placeholder={`الإجابة ${index + 1}`}
                        value={answer.text}
                        onChange={e => {
                          const newAnswers = [...(states.questionEditForm.answers || [])];
                          newAnswers[index] = { ...newAnswers[index], text: e.target.value };
                          handlers.setQuestionEditForm(prev => ({ ...prev, answers: newAnswers }));
                        }}
                        disabled={states.questionEditForm.question.question_type === 'true_false'}
                        required
                      />
                      <label className="flex items-center gap-1">
                        <input
                          type="radio"
                          name={`correct_edit_${states.questionEditForm.question.id}`}
                          checked={answer.is_correct}
                          onChange={() => {
                            const newAnswers = (states.questionEditForm.answers || []).map((ans, i) => ({
                              ...ans,
                              is_correct: i === index
                            }));
                            handlers.setQuestionEditForm(prev => ({ ...prev, answers: newAnswers }));
                          }}
                        />
                        <span className="text-xs">صحيح</span>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-1 rounded text-sm hover:bg-blue-700"
                disabled={states.questionLoading[states.questionEditForm.quizId]}
              >
                {states.questionLoading[states.questionEditForm.quizId] ? 'جاري الحفظ...' : 'حفظ التعديل'}
              </button>
              <button
                type="button"
                className="bg-gray-300 px-4 py-1 rounded text-sm hover:bg-gray-400"
                onClick={() => handlers.setQuestionEditForm(null)}
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}

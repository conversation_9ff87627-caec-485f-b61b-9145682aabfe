// components/course/FileUploader.jsx
"use client";
import React, { useRef, useState } from "react";

export default function FileUploader({ lessonId, onUpload }) {
  const inputRef = useRef();
  const [uploading, setUploading] = useState(false);

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    setUploading(true);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("lesson", lessonId);

      await onUpload(formData);
      inputRef.current.value = ""; // Reset
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="flex items-center gap-4">
      <input
        ref={inputRef}
        type="file"
        accept=".pdf,.doc,.docx,.mp4,.pptx,.txt,.zip"
        onChange={handleFileChange}
        className="block w-full text-sm text-gray-700"
        disabled={uploading}
      />
      {uploading && <span className="text-blue-600 text-sm">جارِ الرفع...</span>}
    </div>
  );
}
